'use client';

import {
  ORDER_STATUS_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  SHIPPING_METHOD_OPTIONS,
} from 'src/actions/mooly-chatbot/order-constants';

/**
 * <PERSON><PERSON><PERSON> hình tập trung cho các field trong order form
 * Tránh duplicate code và đảm bảo đồng bộ hệ thống
 */

// Cấu hình field mapping giữa UI và Database
export const FIELD_MAPPINGS = {
  // Customer fields
  customer: {
    ui: ['fullName', 'email', 'phone', 'avatar', 'avatarUrl'],
    db: ['full_name', 'email', 'phone', 'avatar', 'avatar_url'],
    normalize: (data) => ({
      id: data.id,
      fullName: data.fullName || data.full_name || data.name || '',
      email: data.email || '',
      phone: data.phone || '',
      avatar: data.avatar || data.avatarUrl || data.avatar_url || null,
      avatarUrl: data.avatar || data.avatarUrl || data.avatar_url || null,
      // Backward compatibility
      full_name: data.fullName || data.full_name || data.name || '',
      avatar_url: data.avatar || data.avatarUrl || data.avatar_url || null
    })
  },

  // Address fields
  address: {
    ui: ['fullName', 'phone', 'address', 'addressLine1', 'addressLine2', 'city', 'district', 'ward', 'province', 'postalCode', 'isDefault', 'isDefaultShipping', 'isDefaultBilling'],
    db: ['full_name', 'phone', 'address', 'address_line1', 'address_line2', 'city', 'district', 'ward', 'province', 'postal_code', 'is_default', 'is_default_shipping', 'is_default_billing'],
    normalize: (data) => ({
      id: data.id,
      fullName: data.fullName || data.full_name || '',
      phone: data.phone || '',
      address: data.address || data.addressLine1 || data.address_line1 || '',
      addressLine1: data.address || data.addressLine1 || data.address_line1 || '',
      addressLine2: data.addressLine2 || data.address_line2 || '',
      city: data.city || '',
      district: data.district || '',
      ward: data.ward || '',
      province: data.province || '',
      postalCode: data.postalCode || data.postal_code || '',
      isDefault: data.isDefault || data.is_default || false,
      isDefaultShipping: data.isDefaultShipping || data.is_default_shipping || false,
      isDefaultBilling: data.isDefaultBilling || data.is_default_billing || false,
      // Backward compatibility
      full_name: data.fullName || data.full_name || '',
      address_line1: data.address || data.addressLine1 || data.address_line1 || '',
      address_line2: data.addressLine2 || data.address_line2 || '',
      postal_code: data.postalCode || data.postal_code || '',
      is_default: data.isDefault || data.is_default || false,
      is_default_shipping: data.isDefaultShipping || data.is_default_shipping || false,
      is_default_billing: data.isDefaultBilling || data.is_default_billing || false
    })
  },

  // Product fields
  product: {
    ui: ['name', 'description', 'sellingPrice', 'sku', 'avatar', 'type', 'attributes'],
    db: ['name', 'description', 'selling_price', 'sku', 'avatar', 'type', 'attributes'],
    normalize: (data) => {
      if (!data) return null;

      // Xử lý selling_price - giá bán thực tế duy nhất (tự động convert bởi supabase-utils)
      const sellingPrice = Number(data.sellingPrice || data.selling_price || 0);

      return {
        id: data.id,
        name: data.name || '',
        description: data.description || '',
        shortDescription: data.short_description || data.shortDescription || '',
        price,
        salePrice,
        sku: data.sku || '',
        avatar: data.avatar || null,
        type: data.type || 'simple',
        attributes: data.attributes || {},
        barcode: data.barcode || '',
        stockQuantity: data.stock_quantity || data.stockQuantity || 0,
        isActive: data.is_active !== undefined ? data.is_active : (data.isActive !== undefined ? data.isActive : true),
        // Backward compatibility
        sale_price: salePrice,
        stock_quantity: data.stock_quantity || data.stockQuantity || 0,
        is_active: data.is_active !== undefined ? data.is_active : (data.isActive !== undefined ? data.isActive : true)
      };
    }
  },

  // Variant fields
  variant: {
    ui: ['name', 'sku', 'sellingPrice', 'avatar', 'attributes', 'stockQuantity'],
    db: ['name', 'sku', 'selling_price', 'avatar', 'attributes', 'stock_quantity'],
    normalize: (data) => {
      if (!data) return null;

      // Xử lý selling_price cho variant
      const sellingPrice = Number(data.sellingPrice || data.selling_price || 0);

      // Xử lý price cho variant
      const price = Number(data.price) || 0;

      return {
        id: data.id,
        name: data.name || '',
        sku: data.sku || '',
        price,
        salePrice,
        avatar: data.avatar || null,
        attributes: data.attributes || {},
        stockQuantity: data.stock_quantity || data.stockQuantity || 0,
        barcode: data.barcode || '',
        productId: data.product_id || data.productId || null,
        // Backward compatibility
        sale_price: salePrice,
        stock_quantity: data.stock_quantity || data.stockQuantity || 0,
        product_id: data.product_id || data.productId || null
      };
    }
  },

  // Order item fields
  orderItem: {
    ui: ['productId', 'variantId', 'name', 'sku', 'quantity', 'unitPrice', 'totalPrice', 'imageUrl', 'variantInfo'],
    db: ['product_id', 'variant_id', 'name', 'sku', 'quantity', 'unit_price', 'total_price', 'image_url', 'variant_info'],
    normalize: (data) => {
      if (!data) return null;

      return {
        id: data.id,
        productId: data.product_id || data.productId || '',
        variantId: data.variant_id || data.variantId || '',
        name: data.name || '',
        sku: data.sku || '',
        quantity: Number(data.quantity) || 1,
        unitPrice: Number(data.unit_price || data.unitPrice) || 0,
        totalPrice: Number(data.total_price || data.totalPrice) || 0,
        discountAmount: Number(data.discount_amount || data.discountAmount) || 0,
        taxAmount: Number(data.tax_amount || data.taxAmount) || 0,
        imageUrl: data.image_url || data.imageUrl || null,
        variantInfo: data.variant_info || data.variantInfo || null,
        // Backward compatibility
        product_id: data.product_id || data.productId || '',
        variant_id: data.variant_id || data.variantId || '',
        unit_price: Number(data.unit_price || data.unitPrice) || 0,
        total_price: Number(data.total_price || data.totalPrice) || 0,
        image_url: data.image_url || data.imageUrl || null,
        variant_info: data.variant_info || data.variantInfo || null
      };
    }
  }
};

// Cấu hình các select options
export const SELECT_OPTIONS = {
  orderStatus: ORDER_STATUS_OPTIONS,
  paymentMethod: PAYMENT_METHOD_OPTIONS,
  shippingMethod: SHIPPING_METHOD_OPTIONS
};

// Cấu hình validation rules
export const VALIDATION_RULES = {
  orderNumber: {
    required: true,
    minLength: 1,
    message: 'Vui lòng nhập mã đơn hàng'
  },
  customerName: {
    required: true,
    minLength: 1,
    message: 'Vui lòng nhập tên khách hàng'
  },
  customerPhone: {
    required: true,
    minLength: 1,
    message: 'Vui lòng nhập số điện thoại khách hàng'
  },
  customerEmail: {
    required: false,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Email không hợp lệ'
  },
  quantity: {
    required: true,
    min: 1,
    message: 'Số lượng phải lớn hơn 0'
  },
  unitPrice: {
    required: true,
    min: 0,
    message: 'Đơn giá không được âm'
  }
};

// Cấu hình default values
export const DEFAULT_VALUES = {
  order: {
    orderNumber: '',
    status: 'pending',
    customerId: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    subtotal: 0,
    shippingAmount: 0,
    taxAmount: 0,
    discountAmount: 0,
    totalAmount: 0,
    paymentMethod: 'cod',
    shippingMethod: 'standard',
    shippingAddressId: '',
    billingAddressId: '',
    notes: '',
    orderItems: []
  },
  orderItem: {
    id: null,
    productId: '',
    variantId: '',
    name: '',
    sku: '',
    quantity: 1,
    unitPrice: 0,
    totalPrice: 0,
    discountAmount: 0,
    taxAmount: 0,
    imageUrl: '',
    variantInfo: null,
    productDetail: null,
    variantDetail: null,
    product: null,
    variant: null,
    // Thêm các field để hiển thị thông tin chi tiết
    productType: 'simple',
    variantAttributes: null,
    stockQuantity: 0,
    isInStock: true
  }
};

/**
 * Utility function để xử lý giá một cách nhất quán
 * @param {Object} data - Dữ liệu chứa selling_price
 * @returns {Object} - Object chứa sellingPrice đã xử lý
 */
export function normalizePrice(data) {
  if (!data) return { sellingPrice: 0 };

  const sellingPrice = data.selling_price !== null && data.selling_price !== undefined
    ? Number(data.selling_price)
    : (data.sellingPrice !== null && data.sellingPrice !== undefined ? Number(data.sellingPrice) : 0);

  return { sellingPrice };
}

/**
 * Lấy giá hiển thị (giá bán thực tế)
 * @param {Object} data - Dữ liệu chứa selling_price
 * @returns {number} - Giá hiển thị
 */
export function getDisplayPrice(data) {
  if (!data) return 0;

  const { sellingPrice } = normalizePrice(data);
  return sellingPrice;
}

/**
 * Debug function để kiểm tra dữ liệu normalize
 * @param {Object} data - Dữ liệu gốc
 * @param {string} type - Loại dữ liệu
 * @returns {Object} - Thông tin debug
 */
export function debugNormalizeData(data, type) {
  const original = data;
  const normalized = normalizeFieldData(data, type);
  const displayPrice = type === 'product' || type === 'variant' ? getDisplayPrice(normalized) : null;

  console.log(`🔍 Debug ${type} data:`, {
    original,
    normalized,
    displayPrice,
    priceInfo: type === 'product' || type === 'variant' ? {
      originalPrice: original?.price,
      originalSalePrice: original?.sale_price || original?.salePrice,
      normalizedPrice: normalized?.price,
      normalizedSalePrice: normalized?.salePrice,
      finalDisplayPrice: displayPrice
    } : null
  });

  return { original, normalized, displayPrice };
}

/**
 * Normalize dữ liệu theo type
 * @param {Array|Object} data - Dữ liệu cần normalize
 * @param {string} type - Loại dữ liệu (customer, address, product, variant, orderItem)
 * @returns {Array|Object} - Dữ liệu đã normalize
 */
export function normalizeFieldData(data, type) {
  if (!data) return null;

  const mapping = FIELD_MAPPINGS[type];
  if (!mapping) return data;

  if (Array.isArray(data)) {
    return data.map(item => mapping.normalize(item)).filter(Boolean);
  }

  return mapping.normalize(data);
}

/**
 * Utility function để format thông tin biến thể cho hiển thị
 * @param {Object} variantInfo - Thông tin biến thể
 * @returns {Array} - Mảng các cặp key-value đã format
 */
export function formatVariantAttributes(variantInfo) {
  if (!variantInfo || typeof variantInfo !== 'object') return [];

  return Object.entries(variantInfo)
    .filter(([key, value]) => value !== null && value !== undefined && value !== '')
    .map(([key, value]) => ({
      key: key.charAt(0).toUpperCase() + key.slice(1), // Capitalize first letter
      value: String(value),
      display: `${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`
    }));
}

/**
 * Utility function để xác định loại sản phẩm
 * @param {Object} item - Order item
 * @returns {Object} - Thông tin loại sản phẩm
 */
export function getProductTypeInfo(item) {
  const hasVariant = Boolean(item.variantId);
  const productType = item.productType || item.productDetail?.type || 'simple';

  return {
    hasVariant,
    productType,
    isVariable: productType === 'variable' || hasVariant,
    displayType: hasVariant ? 'Biến thể' : 'Sản phẩm đơn giản',
    color: hasVariant ? 'info' : 'primary'
  };
}

/**
 * Utility function để lấy hình ảnh sản phẩm ưu tiên
 * @param {Object} item - Order item
 * @returns {string|null} - URL hình ảnh
 */
export function getProductImage(item) {
  return item.imageUrl ||
         item.image_url ||
         item.variantDetail?.avatar ||
         item.variant?.avatar ||
         item.productDetail?.avatar ||
         item.product?.avatar ||
         item.productDetail?.images?.[0] ||
         item.product?.images?.[0] ||
         null;
}

/**
 * Utility function để kiểm tra tình trạng tồn kho
 * @param {Object} item - Order item
 * @returns {Object} - Thông tin tồn kho
 */
export function getStockInfo(item) {
  const stockQuantity = item.stockQuantity ||
                       item.variantDetail?.stockQuantity ||
                       item.productDetail?.stockQuantity ||
                       0;

  const isInStock = item.isInStock !== undefined ?
                   item.isInStock :
                   stockQuantity > 0;

  return {
    stockQuantity,
    isInStock,
    displayText: isInStock ?
      `Còn hàng${stockQuantity ? ` (${stockQuantity})` : ''}` :
      'Hết hàng',
    color: isInStock ? 'success' : 'error'
  };
}

/**
 * Tạo default values cho form
 * @param {Object} data - Dữ liệu ban đầu
 * @param {boolean} isEdit - Có phải chế độ edit không
 * @returns {Object} - Default values cho form
 */
export function createDefaultValues(data = null, isEdit = false) {
  const defaults = { ...DEFAULT_VALUES.order };

  if (data && isEdit) {
    // Merge với dữ liệu hiện có
    Object.keys(defaults).forEach(key => {
      if (data[key] !== undefined) {
        defaults[key] = data[key];
      }
    });

    // Normalize order items
    if (data.orderItems && Array.isArray(data.orderItems)) {
      defaults.orderItems = data.orderItems.map(item => ({
        ...DEFAULT_VALUES.orderItem,
        ...item,
        productDetail: item.productDetail || item.products || null,
        variantDetail: item.variantDetail || item.variants || null,
        product: null, // Sẽ được set sau khi load products
        variant: null  // Sẽ được set sau khi load variants
      }));
    }
  }

  return defaults;
}

/**
 * Tạo order item mới với default values
 * @param {Object} productData - Dữ liệu sản phẩm
 * @param {Object} variantData - Dữ liệu biến thể (optional)
 * @returns {Object} - Order item mới
 */
export function createOrderItem(productData = {}, variantData = null) {
  // Chuẩn hóa dữ liệu trước khi sử dụng
  const normalizedProduct = normalizeFieldData(productData, 'product');
  const normalizedVariant = variantData ? normalizeFieldData(variantData, 'variant') : null;

  // Sử dụng getDisplayPrice để lấy giá chính xác
  const unitPrice = normalizedVariant ? getDisplayPrice(normalizedVariant) : getDisplayPrice(normalizedProduct);

  const baseItem = {
    ...DEFAULT_VALUES.orderItem,
    productId: normalizedProduct?.id || '',
    variantId: normalizedVariant?.id || '',
    name: normalizedVariant?.name || normalizedProduct?.name || '',
    sku: normalizedVariant?.sku || normalizedProduct?.sku || '',
    unitPrice,
    productDetail: normalizedProduct,
    variantDetail: normalizedVariant,
    productType: normalizedProduct?.type || 'simple',
    variantInfo: normalizedVariant?.attributes || null,
    variantAttributes: normalizedVariant?.attributes || null
  };

  // Tính toán các field phụ thuộc
  baseItem.totalPrice = baseItem.unitPrice * baseItem.quantity;
  baseItem.imageUrl = getProductImage(baseItem);

  const stockInfo = getStockInfo(baseItem);
  baseItem.stockQuantity = stockInfo.stockQuantity;
  baseItem.isInStock = stockInfo.isInStock;

  // Debug log để kiểm tra dữ liệu
  console.log('🔧 createOrderItem:', {
    productData,
    variantData,
    normalizedProduct,
    normalizedVariant,
    unitPrice,
    baseItem
  });

  return baseItem;
}

/**
 * Transform dữ liệu form thành dữ liệu database
 * Loại bỏ các field UI-only và normalize field names
 * @param {Object} formData - Dữ liệu từ form
 * @returns {Object} - Dữ liệu đã transform cho database
 */
export function transformFormDataToDatabase(formData) {
  // Loại bỏ các field UI-only
  const {
    orderItems,
    customer, // Loại bỏ field customer vì database chỉ có customer_id
    ...orderData
  } = formData;

  // Transform order items - loại bỏ các field UI-only
  const transformedOrderItems = orderItems?.map(item => {
    const {
      productDetail, // Loại bỏ field UI-only
      variantDetail, // Loại bỏ field UI-only
      product, // Loại bỏ field UI-only
      variant, // Loại bỏ field UI-only
      productType, // Loại bỏ field UI-only
      variantAttributes, // Loại bỏ field UI-only
      stockQuantity, // Loại bỏ field UI-only
      isInStock, // Loại bỏ field UI-only
      ...itemData
    } = item;

    return {
      ...itemData,
      // Đảm bảo variantId là null thay vì empty string
      variantId: itemData.variantId || null,
      // Giữ lại imageUrl và variantInfo cho database
      imageUrl: itemData.imageUrl || null,
      variantInfo: itemData.variantInfo || null,
    };
  }) || [];

  return {
    ...orderData,
    orderItems: transformedOrderItems,
  };
}

/**
 * Validate field theo rules
 * @param {string} fieldName - Tên field
 * @param {any} value - Giá trị cần validate
 * @returns {string|null} - Error message hoặc null nếu valid
 */
export function validateField(fieldName, value) {
  const rule = VALIDATION_RULES[fieldName];
  if (!rule) return null;

  if (rule.required && (!value || value.toString().trim() === '')) {
    return rule.message;
  }

  if (rule.minLength && value && value.toString().length < rule.minLength) {
    return rule.message;
  }

  if (rule.min !== undefined && value !== undefined && Number(value) < rule.min) {
    return rule.message;
  }

  if (rule.pattern && value && !rule.pattern.test(value.toString())) {
    return rule.message;
  }

  return null;
}
