/**
 * Utility functions để xử lý giá sản phẩm
 * Tối ưu cho hệ thống đơn giản hóa với selling_price duy nhất
 * Sử dụng supabase-utils để tự động convert giữa camelCase (UI) và snake_case (DB)
 */

/**
 * Lấy giá hiển thị từ dữ liệu sản phẩm
 * @param {Object} product - Dữ liệu sản phẩm
 * @returns {number} - Giá hiển thị
 */
export function getProductDisplayPrice(product) {
  if (!product) return 0;

  // Ưu tiên sellingPrice (UI format) trước, sau đó selling_price (DB format)
  return Number(product.sellingPrice || product.selling_price || 0);
}

/**
 * Format giá tiền theo định dạng VND
 * @param {number} price - Giá tiền
 * @returns {string} - Giá đã format
 */
export function formatPrice(price) {
  if (!price || price === 0) return '0 ₫';
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

/**
 * Validate giá sản phẩm
 * @param {number} price - Giá cần validate
 * @returns {Object} - Kết quả validation
 */
export function validatePrice(price) {
  if (price === null || price === undefined) {
    return { isValid: false, error: 'Giá bán là bắt buộc' };
  }

  const numPrice = Number(price);
  
  if (isNaN(numPrice)) {
    return { isValid: false, error: 'Giá bán phải là số' };
  }

  if (numPrice < 0) {
    return { isValid: false, error: 'Giá bán phải lớn hơn hoặc bằng 0' };
  }

  return { isValid: true, error: null };
}

/**
 * Tính toán lợi nhuận từ giá bán và giá vốn
 * @param {number} sellingPrice - Giá bán
 * @param {number} costPrice - Giá vốn
 * @returns {Object} - Thông tin lợi nhuận
 */
export function calculateProfit(sellingPrice, costPrice) {
  const selling = Number(sellingPrice) || 0;
  const cost = Number(costPrice) || 0;

  if (!selling || !cost) {
    return { profit: 0, profitMargin: 0 };
  }

  const profit = selling - cost;
  const profitMargin = (profit / selling) * 100;

  return {
    profit: Math.round(profit),
    profitMargin: Math.round(profitMargin * 100) / 100, // Làm tròn 2 chữ số thập phân
  };
}

/**
 * Normalize giá sản phẩm từ nhiều nguồn khác nhau
 * @param {Object} product - Dữ liệu sản phẩm
 * @returns {number} - Giá đã normalize
 */
export function normalizeProductPrice(product) {
  if (!product) return 0;

  // Hỗ trợ cả format cũ và mới để đảm bảo tương thích
  return Number(
    product.sellingPrice ||
    product.selling_price ||
    product.price ||
    product.salePrice ||
    product.sale_price ||
    0
  );
}
