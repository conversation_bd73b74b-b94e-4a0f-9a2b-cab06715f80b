import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import TableContainer from '@mui/material/TableContainer';

import { Iconify } from 'src/components/iconify';
import { FileThumbnail } from 'src/components/file-thumbnail';
import { CurrencyInput } from 'src/components/currency-input';

/**
 * Component hiển thị bảng biến thể sản phẩm
 */
export default function VariantTable({
  variants,
  handleVariantChange,
  handleOpenImageSelect,
  handleRemoveVariantImage,
  isEditMode = false,
}) {
  if (!variants.length) return null;

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Danh sách biến thể ({variants.length})
      </Typography>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Ảnh</TableCell>
              <TableCell>Biến thể</TableCell>
              <TableCell>SKU</TableCell>
              <TableCell>Giá bán</TableCell>
              <TableCell>Giá KM</TableCell>
              <TableCell>Giá nhập</TableCell>
              <TableCell>Tồn kho</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {variants.map((variant) => (
              <TableRow key={variant.id}>
                <TableCell>
                  <Box sx={{ position: 'relative', display: 'inline-block' }}>
                    {variant.avatar ? (
                      <Box sx={{ position: 'relative' }}>
                        <Box
                          sx={{
                            width: 64,
                            height: 64,
                            cursor: 'pointer',
                            borderRadius: 1,
                            overflow: 'hidden',
                          }}
                          onClick={() => handleOpenImageSelect(variant.id)}
                        >
                          <FileThumbnail
                            file={variant.avatar}
                            imageView
                            sx={{ width: '100%', height: '100%' }}
                            slotProps={{
                              img: {
                                sx: {
                                  objectFit: 'cover',
                                },
                              },
                            }}
                          />
                        </Box>
                        <IconButton
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: -8,
                            right: -8,
                            backgroundColor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { backgroundColor: 'error.lighter' },
                          }}
                          onClick={() => handleRemoveVariantImage(variant.id)}
                        >
                          <Iconify icon="eva:close-fill" width={16} />
                        </IconButton>
                      </Box>
                    ) : (
                      <Tooltip title="Chọn ảnh cho biến thể">
                        <Button
                          variant="outlined"
                          sx={{ width: 64, height: 64, minWidth: 'auto', p: 0 }}
                          onClick={() => handleOpenImageSelect(variant.id)}
                        >
                          <Iconify icon="eva:image-fill" width={24} />
                        </Button>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
                <TableCell>{variant.name}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TextField
                      size="small"
                      value={variant.sku || ''}
                      onChange={(e) => handleVariantChange(variant.id, 'sku', e.target.value)}
                      placeholder="SKU tự động"
                      helperText={!variant.sku ? 'SKU sẽ được tạo tự động khi lưu' : ''}
                      InputProps={{
                        style: { fontSize: '0.875rem' }
                      }}
                      sx={{ flexGrow: 1 }}
                    />
                    <IconButton
                      size="small"
                      onClick={() => handleVariantChange(variant.id, 'sku', '')}
                      title="Tạo lại SKU tự động"
                      sx={{ p: 0.5 }}
                    >
                      <Iconify icon="eva:refresh-fill" width={16} />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>
                  <CurrencyInput
                    size="small"
                    value={variant.sellingPrice || ''}
                    onChange={(_, value) => handleVariantChange(variant.id, 'sellingPrice', value)}
                    placeholder="Giá bán"
                    currencySymbol="VND"
                    required
                  />
                </TableCell>
                <TableCell>
                  <CurrencyInput
                    size="small"
                    value={variant.costPrice || ''}
                    onChange={(_, value) => handleVariantChange(variant.id, 'costPrice', value)}
                    placeholder="Giá nhập"
                    currencySymbol="VND"
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    size="small"
                    type="number"
                    value={variant.stockQuantity || ''}
                    onChange={(e) =>
                      handleVariantChange(variant.id, 'stockQuantity', Number(e.target.value))
                    }
                    placeholder="Tồn kho"
                    required
                    disabled={isEditMode}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

VariantTable.propTypes = {
  variants: PropTypes.array.isRequired,
  handleVariantChange: PropTypes.func.isRequired,
  handleOpenImageSelect: PropTypes.func.isRequired,
  handleRemoveVariantImage: PropTypes.func.isRequired,
  isEditMode: PropTypes.bool,
};
