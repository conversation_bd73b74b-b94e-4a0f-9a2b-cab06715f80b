import useSWR from 'swr';

import { fetchData, createData, deleteData } from 'src/actions/mooly-chatbot/supabase-utils';

// ----------------------------------------------------------------------

/**
 * Helper function để xác định trạng thái sản phẩm
 * @param {boolean} isActive - Sản phẩm có đang active không
 * @param {number} stockQuantity - Số lượng tồn kho
 * @param {boolean} trackInventory - Có track inventory không
 * @returns {object} - Object chứa trạng thái và thông tin hiển thị
 */
function getProductStatus(isActive, stockQuantity, trackInventory) {
  if (!isActive) {
    return {
      type: 'inactive',
      label: 'Ngừng bán',
      color: 'error'
    };
  }
  
  if (trackInventory && stockQuantity <= 0) {
    return {
      type: 'out_of_stock',
      label: 'Hết hàng',
      color: 'warning'
    };
  }
  
  return {
    type: 'active',
    label: 'Đang bán',
    color: 'success'
  };
}

/**
 * Hook để lấy danh sách sản phẩm của chatbot
 * @param {string} chatbotId - ID của chatbot
 * @returns {object} - Dữ liệu sản phẩm, trạng thái loading và error
 */
export function useChatbotProducts(chatbotId) {
  const { data, error, isLoading, mutate } = useSWR(
    chatbotId ? `chatbot-products-${chatbotId}` : null,
    async () => {
      if (!chatbotId) return { products: [] };
      
      // Sử dụng supabase-utils để fetch data - field names sẽ được tự động convert
      const result = await fetchData('chatbot_products', {
        columns: `
          product_id,
          created_at,
          products!inner (
            id,
            name,
            selling_price,
            stock_quantity,
            description,
            avatar,
            sku,
            is_active,
            track_inventory
          )
        `,
        filters: {
          chatbotId: chatbotId,
          'products.is_active': true
        },
        orderBy: 'createdAt',
        ascending: false
      });

      if (!result.success) {
        throw result.error;
      }

      // supabase-utils đã convert snake_case -> camelCase
      // result.data sẽ có productId, createdAt, etc.
      const products = result.data?.map((item) => ({
        ...item.products,
        addedToChatbotAt: item.createdAt,
        // Logic trạng thái chính xác
        status: getProductStatus(item.products.isActive, item.products.stockQuantity, item.products.trackInventory)
      })) || [];

      return { products };
    },
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 0, // Không auto refresh để tránh spam requests
      dedupingInterval: 1000, // Dedupe requests trong 1 giây
    }
  );

  return {
    products: data?.products || [],
    isLoading,
    error,
    mutate,
  };
}

/**
 * Thêm sản phẩm vào chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array<string>} productIds - Danh sách ID sản phẩm
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<object>} - Kết quả thêm sản phẩm
 */
export async function addProductsToChatbot(chatbotId, productIds, tenantId) {
  if (!productIds || productIds.length === 0) {
    throw new Error('No products to add');
  }

  // Tối ưu: Kiểm tra products đã tồn tại trong chatbot trước
  const existingResult = await fetchData('chatbot_products', {
    columns: 'product_id',
    filters: {
      chatbotId: chatbotId,
      productId: { operator: 'in', value: productIds }
    }
  });

  if (!existingResult.success) {
    throw new Error('Failed to check existing products: ' + existingResult.error.message);
  }

  const existingProductIds = existingResult.data?.map(item => item.productId) || [];
  const newProductIds = productIds.filter(id => !existingProductIds.includes(id));

  if (newProductIds.length === 0) {
    return { 
      message: 'All products already exist in chatbot', 
      count: 0 
    };
  }

  // Lấy thông tin sản phẩm từ Supabase (chỉ những sản phẩm mới)
  const productsResult = await fetchData('products', {
    columns: 'id, name, price, stock_quantity, description, avatar',
    filters: {
      id: { operator: 'in', value: newProductIds },
      isActive: true
    }
  });

  if (!productsResult.success) {
    throw new Error('Failed to fetch products: ' + productsResult.error.message);
  }

  if (!productsResult.data || productsResult.data.length === 0) {
    throw new Error('No valid products found');
  }

  // Batch insert vào bảng chatbot_products
  const chatbotProductsData = productsResult.data.map((product) => ({
    chatbotId: chatbotId,
    productId: product.id,
    tenantId: tenantId,
  }));

  const insertResult = await createData('chatbot_products', chatbotProductsData, false);

  if (!insertResult.success) {
    throw new Error('Failed to add products to chatbot: ' + insertResult.error.message);
  }

  // Chuẩn bị dữ liệu để đồng bộ với Weaviate (batch processing)
  const weaviateProducts = productsResult.data.map((product) => ({
    name: product.name,
    price: product.price,
    stock: product.stockQuantity, // supabase-utils đã convert stock_quantity -> stockQuantity
    description: product.description,
    image_url: product.avatar,
    tenant_id: tenantId,
    bot_id: chatbotId,
    product_id: product.id,
  }));

  // Đồng bộ với Weaviate (async, không block main flow)
  fetch('/api/weaviate/products/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ products: weaviateProducts }),
  }).catch(error => {
    console.error('Weaviate sync error (non-blocking):', error);
  });

  return { 
    message: 'Products added to chatbot successfully', 
    count: productsResult.data.length 
  };
}

/**
 * Xóa sản phẩm khỏi chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array<string>} productIds - Danh sách ID sản phẩm
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<object>} - Kết quả xóa sản phẩm
 */
export async function removeProductsFromChatbot(chatbotId, productIds, tenantId) {
  if (!productIds || productIds.length === 0) {
    throw new Error('No products to remove');
  }

  // Tối ưu: Kiểm tra products có tồn tại trong chatbot không trước khi xóa
  const existingResult = await fetchData('chatbot_products', {
    columns: 'product_id',
    filters: {
      chatbotId: chatbotId,
      productId: { operator: 'in', value: productIds }
    }
  });

  if (!existingResult.success) {
    throw new Error('Failed to check existing products: ' + existingResult.error.message);
  }

  const existingProductIds = existingResult.data?.map(item => item.productId) || [];
  
  if (existingProductIds.length === 0) {
    return { 
      message: 'No products found in chatbot to remove',
      count: 0
    };
  }

  // Batch delete khỏi bảng chatbot_products
  const deleteResult = await deleteData('chatbot_products', {
    chatbotId: chatbotId,
    productId: { operator: 'in', value: existingProductIds }
  });

  if (!deleteResult.success) {
    throw new Error('Failed to remove products from chatbot: ' + deleteResult.error.message);
  }

  // Đồng bộ với Weaviate (async, không block main flow)
  fetch('/api/weaviate/products/delete-by-ids', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ 
      product_ids: existingProductIds,
      tenant_id: tenantId 
    }),
  }).catch(error => {
    console.error('Weaviate deletion sync error (non-blocking):', error);
  });

  return { 
    message: 'Products removed from chatbot successfully',
    count: existingProductIds.length
  };
}

/**
 * Kiểm tra sản phẩm có thuộc chatbot hay không
 * @param {string} chatbotId - ID của chatbot
 * @param {string} productId - ID của sản phẩm
 * @param {Array} chatbotProducts - Danh sách sản phẩm của chatbot
 * @returns {boolean} - True nếu sản phẩm thuộc chatbot
 */
export function isProductInChatbot(chatbotId, productId, chatbotProducts = []) {
  return chatbotProducts.some((product) => product.id === productId);
}

/**
 * Lọc sản phẩm chưa được thêm vào chatbot
 * @param {Array} allProducts - Danh sách tất cả sản phẩm
 * @param {Array} chatbotProducts - Danh sách sản phẩm của chatbot
 * @returns {Array} - Danh sách sản phẩm chưa được thêm vào chatbot
 */
export function getProductsNotInChatbot(allProducts = [], chatbotProducts = []) {
  const chatbotProductIds = chatbotProducts.map((product) => product.id);
  return allProducts.filter((product) => !chatbotProductIds.includes(product.id));
}

/**
 * Utility function để invalidate SWR cache cho chatbot products
 * @param {string} chatbotId - ID của chatbot
 */
export function invalidateChatbotProductsCache(chatbotId) {
  if (typeof window !== 'undefined' && window.mutate) {
    // Invalidate cache for specific chatbot
    window.mutate(key => typeof key === 'string' && key.includes(`chatbot-products-${chatbotId}`));
    // Also invalidate general products cache if exists
    window.mutate(key => typeof key === 'string' && key.includes('products'));
  }
} 