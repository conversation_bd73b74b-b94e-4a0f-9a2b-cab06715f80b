/**
 * Utility functions để xử lý giá sản phẩm
 * Tối ưu cho hệ thống đơn giản hóa với selling_price duy nhất
 */

/**
 * Chuyển đổi dữ liệu từ database format sang UI format
 * @param {Object} data - Dữ liệu từ database
 * @returns {Object} - Dữ liệu đã chuyển đổi cho UI
 */
export function convertDbToUiFormat(data) {
  if (!data) return null;

  return {
    ...data,
    // Chuyển đổi selling_price từ DB sang sellingPrice cho UI
    sellingPrice: data.selling_price || data.sellingPrice || 0,
    // Đảm bảo tương thích ngược
    price: data.selling_price || data.sellingPrice || 0,
  };
}

/**
 * Chuyển đổi dữ liệu từ UI format sang database format
 * @param {Object} data - Dữ liệu từ UI
 * @returns {Object} - Dữ liệu đã chuyển đổi cho database
 */
export function convertUiToDbFormat(data) {
  if (!data) return null;

  const result = { ...data };
  
  // Chuyển đổi sellingPrice từ UI sang selling_price cho DB
  if (data.sellingPrice !== undefined) {
    result.selling_price = Number(data.sellingPrice) || 0;
    // Xóa field UI để tránh conflict
    delete result.sellingPrice;
  }

  // Xóa các field cũ nếu có
  delete result.price;
  delete result.salePrice;
  delete result.sale_price;

  return result;
}

/**
 * Lấy giá hiển thị từ dữ liệu sản phẩm
 * @param {Object} product - Dữ liệu sản phẩm
 * @returns {number} - Giá hiển thị
 */
export function getProductDisplayPrice(product) {
  if (!product) return 0;
  
  return product.selling_price || product.sellingPrice || 0;
}

/**
 * Format giá tiền theo định dạng VND
 * @param {number} price - Giá tiền
 * @returns {string} - Giá đã format
 */
export function formatPrice(price) {
  if (!price || price === 0) return '0 ₫';
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

/**
 * Validate giá sản phẩm
 * @param {number} price - Giá cần validate
 * @returns {Object} - Kết quả validation
 */
export function validatePrice(price) {
  if (price === null || price === undefined) {
    return { isValid: false, error: 'Giá bán là bắt buộc' };
  }

  const numPrice = Number(price);
  
  if (isNaN(numPrice)) {
    return { isValid: false, error: 'Giá bán phải là số' };
  }

  if (numPrice < 0) {
    return { isValid: false, error: 'Giá bán phải lớn hơn hoặc bằng 0' };
  }

  return { isValid: true, error: null };
}

/**
 * Tính toán lợi nhuận từ giá bán và giá vốn
 * @param {number} sellingPrice - Giá bán
 * @param {number} costPrice - Giá vốn
 * @returns {Object} - Thông tin lợi nhuận
 */
export function calculateProfit(sellingPrice, costPrice) {
  if (!sellingPrice || !costPrice) {
    return { profit: 0, profitMargin: 0 };
  }

  const profit = sellingPrice - costPrice;
  const profitMargin = (profit / sellingPrice) * 100;

  return {
    profit: Math.round(profit),
    profitMargin: Math.round(profitMargin * 100) / 100, // Làm tròn 2 chữ số thập phân
  };
}

/**
 * Batch convert nhiều sản phẩm từ DB format sang UI format
 * @param {Array} products - Danh sách sản phẩm từ DB
 * @returns {Array} - Danh sách sản phẩm đã convert
 */
export function batchConvertDbToUi(products) {
  if (!Array.isArray(products)) return [];
  
  return products.map(convertDbToUiFormat);
}

/**
 * Batch convert nhiều sản phẩm từ UI format sang DB format
 * @param {Array} products - Danh sách sản phẩm từ UI
 * @returns {Array} - Danh sách sản phẩm đã convert
 */
export function batchConvertUiToDb(products) {
  if (!Array.isArray(products)) return [];
  
  return products.map(convertUiToDbFormat);
}
