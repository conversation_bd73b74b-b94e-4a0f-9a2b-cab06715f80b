'use client';

import { isVariableProduct } from './product-constants';

/**
 * Utility functions để chuẩn hóa dữ liệu order cho form
 */

/**
 * Chuẩn hóa thông tin khách hàng
 * @param {Object} customer - Thông tin khách hàng từ database
 * @returns {Object} - Thông tin khách hàng đã chuẩn hóa
 */
export function normalizeCustomerInfo(customer) {
  if (!customer) return null;

  return {
    id: customer.id,
    fullName: customer.fullName || customer.full_name || customer.name || '',
    email: customer.email || '',
    phone: customer.phone || '',
    avatar: customer.avatar || customer.avatarUrl || customer.avatar_url || null,
    avatarUrl: customer.avatar || customer.avatarUrl || customer.avatar_url || null,
    notes: customer.notes || '',
    // Thêm các field phụ để đảm bảo tương thích
    full_name: customer.fullName || customer.full_name || customer.name || '',
    avatar_url: customer.avatar || customer.avatarUrl || customer.avatar_url || null
  };
}

/**
 * Chuẩn hóa thông tin địa chỉ
 * @param {Object} address - Thông tin địa chỉ từ database
 * @returns {Object} - Thông tin địa chỉ đã chuẩn hóa
 */
export function normalizeAddressInfo(address) {
  if (!address) return null;

  return {
    id: address.id,
    fullName: address.fullName || address.full_name || '',
    phone: address.phone || '',
    address: address.address || address.addressLine1 || address.address_line1 || '',
    addressLine1: address.address || address.addressLine1 || address.address_line1 || '',
    addressLine2: address.addressLine2 || address.address_line2 || '',
    city: address.city || '',
    state: address.state || '',
    postalCode: address.postalCode || address.postal_code || '',
    country: address.country || '',
    province: address.province || '',
    district: address.district || '',
    ward: address.ward || '',
    notes: address.notes || '',
    addressType: address.addressType || address.address_type || '',
    isDefault: address.isDefault || address.is_default || false,
    isDefaultShipping: address.isDefaultShipping || address.is_default_shipping || false,
    isDefaultBilling: address.isDefaultBilling || address.is_default_billing || false,
    // Thêm các field phụ để đảm bảo tương thích
    full_name: address.fullName || address.full_name || '',
    address_line1: address.address || address.addressLine1 || address.address_line1 || '',
    address_line2: address.addressLine2 || address.address_line2 || '',
    postal_code: address.postalCode || address.postal_code || '',
    address_type: address.addressType || address.address_type || '',
    is_default: address.isDefault || address.is_default || false,
    is_default_shipping: address.isDefaultShipping || address.is_default_shipping || false,
    is_default_billing: address.isDefaultBilling || address.is_default_billing || false
  };
}

/**
 * Chuẩn hóa thông tin sản phẩm
 * @param {Object} product - Thông tin sản phẩm từ database
 * @returns {Object} - Thông tin sản phẩm đã chuẩn hóa
 */
export function normalizeProductInfo(product) {
  if (!product) return null;

  return {
    id: product.id,
    name: product.name || '',
    description: product.description || '',
    sellingPrice: Number(product.sellingPrice || product.selling_price || 0),
    images: product.images || [],
    avatar: product.avatar || null,
    type: product.type || 'simple',
    sku: product.sku || '',
    barcode: product.barcode || '',
    attributes: product.attributes || {},
    // hasVariants được xác định dựa trên type sử dụng helper function
    hasVariants: isVariableProduct(product),
    has_variants: isVariableProduct(product)
  };
}

/**
 * Chuẩn hóa thông tin biến thể sản phẩm
 * @param {Object} variant - Thông tin biến thể từ database
 * @returns {Object} - Thông tin biến thể đã chuẩn hóa
 */
export function normalizeVariantInfo(variant) {
  if (!variant) return null;

  return {
    id: variant.id,
    name: variant.name || '',
    sku: variant.sku || '',
    barcode: variant.barcode || '',
    sellingPrice: Number(variant.sellingPrice || variant.selling_price || 0),
    attributes: variant.attributes || {},
    avatar: variant.avatar || null,
    stockQuantity: variant.stockQuantity || variant.stock_quantity || 0,
    // Thêm các field phụ để đảm bảo tương thích
    stock_quantity: variant.stockQuantity || variant.stock_quantity || 0
  };
}

/**
 * Chuẩn hóa thông tin order item
 * @param {Object} item - Thông tin order item từ database
 * @returns {Object} - Thông tin order item đã chuẩn hóa
 */
export function normalizeOrderItem(item) {
  if (!item) return null;

  const productDetail = normalizeProductInfo(item.products || item.product);
  const variantDetail = normalizeVariantInfo(item.variants || item.variant);

  // Xử lý thông tin tồn kho
  const stockQuantity = variantDetail?.stockQuantity || productDetail?.stockQuantity || 0;
  const isInStock = stockQuantity > 0;

  return {
    ...item,
    // Chuẩn hóa các field cơ bản
    productId: item.product_id || item.productId || '',
    variantId: item.variant_id || item.variantId || '',
    quantity: Number(item.quantity) || 1,
    unitPrice: Number(item.unit_price || item.unitPrice) || 0,
    totalPrice: Number(item.total_price || item.totalPrice) || 0,
    variantInfo: item.variant_info || item.variantInfo || null,

    // Thông tin chi tiết
    productDetail,
    variantDetail,
    productType: productDetail?.type || 'simple',

    // Xử lý thông tin variantInfo nếu có
    variantAttributes: item.variantInfo || item.variant_info || variantDetail?.attributes || {},

    // Ưu tiên lấy hình ảnh từ variant, nếu không có thì lấy từ product
    imageUrl: item.image_url || item.imageUrl ||
              variantDetail?.avatar ||
              productDetail?.avatar ||
              productDetail?.images?.[0] ||
              null,

    // Thông tin tồn kho
    stockQuantity,
    isInStock,

    // Backward compatibility
    product_id: item.product_id || item.productId || '',
    variant_id: item.variant_id || item.variantId || '',
    unit_price: Number(item.unit_price || item.unitPrice) || 0,
    total_price: Number(item.total_price || item.totalPrice) || 0,
    image_url: item.image_url || item.imageUrl || null,
    variant_info: item.variant_info || item.variantInfo || null
  };
}

/**
 * Chuẩn hóa dữ liệu order đầy đủ
 * @param {Object} order - Dữ liệu order từ database
 * @param {Array} orderItems - Danh sách order items
 * @returns {Object} - Dữ liệu order đã chuẩn hóa cho form
 */
export function normalizeOrderData(order, orderItems = []) {
  if (!order) return null;

  const normalizedOrder = {
    ...order,
    // Chuẩn hóa thông tin khách hàng
    customerInfo: normalizeCustomerInfo(order.customers || order.customer),
    // Chuẩn hóa thông tin địa chỉ
    shippingAddressInfo: normalizeAddressInfo(order.shippingAddresses || order.shipping_address),
    billingAddressInfo: normalizeAddressInfo(order.billingAddresses || order.billing_address),
    // Chuẩn hóa order items
    orderItems: orderItems.map(normalizeOrderItem)
  };

  return normalizedOrder;
}

/**
 * Chuẩn hóa dữ liệu form để gửi lên server
 * @param {Object} formData - Dữ liệu từ form
 * @returns {Object} - Dữ liệu đã chuẩn hóa để gửi lên server
 */
export function normalizeFormDataForServer(formData) {
  if (!formData) return null;

  // Loại bỏ các field không cần thiết cho server
  const {
    ...serverData
  } = formData;

  // Chuẩn hóa order items để gửi lên server
  if (serverData.orderItems) {
    serverData.orderItems = serverData.orderItems.map(item => {
      const {
        variantAttributes: itemVariantAttributes,
        ...serverItem
      } = item;

      return {
        ...serverItem,
        // Đảm bảo variantInfo được format đúng
        variantInfo: itemVariantAttributes || item.variantInfo || null
      };
    });
  }

  return serverData;
}

/**
 * Kiểm tra và sửa lỗi field mapping
 * @param {Object} data - Dữ liệu cần kiểm tra
 * @returns {Object} - Dữ liệu đã được sửa lỗi mapping
 */
export function fixFieldMapping(data) {
  if (!data || typeof data !== 'object') return data;

  // Mapping các field thường gặp lỗi
  const fieldMappings = {
    full_name: 'fullName',
    avatar_url: 'avatarUrl',
    address_line1: 'addressLine1',
    address_line2: 'addressLine2',
    postal_code: 'postalCode',
    address_type: 'addressType',
    is_default: 'isDefault',
    is_default_shipping: 'isDefaultShipping',
    is_default_billing: 'isDefaultBilling',
    sale_price: 'salePrice',
    has_variants: 'hasVariants',
    stock_quantity: 'stockQuantity',
    variant_info: 'variantInfo',
    image_url: 'imageUrl'
  };

  const fixedData = { ...data };

  // Áp dụng mapping
  Object.entries(fieldMappings).forEach(([snakeCase, camelCase]) => {
    if (fixedData[snakeCase] !== undefined && fixedData[camelCase] === undefined) {
      fixedData[camelCase] = fixedData[snakeCase];
    }
  });

  return fixedData;
}

/**
 * Chuẩn hóa dữ liệu cho autocomplete components
 * @param {Array} options - Danh sách options
 * @param {string} type - Loại dữ liệu (customer, product, variant, address)
 * @returns {Array} - Danh sách options đã chuẩn hóa
 */
export function normalizeAutocompleteOptions(options, type) {
  if (!Array.isArray(options)) return [];

  switch (type) {
    case 'customer':
      return options.map(normalizeCustomerInfo).filter(Boolean);
    case 'product':
      return options.map(normalizeProductInfo).filter(Boolean);
    case 'variant':
      return options.map(normalizeVariantInfo).filter(Boolean);
    case 'address':
      return options.map(normalizeAddressInfo).filter(Boolean);
    default:
      return options.map(fixFieldMapping);
  }
}
