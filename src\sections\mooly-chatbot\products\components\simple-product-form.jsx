'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import MenuItem from '@mui/material/MenuItem';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';

import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';
import { FORM_FIELDS, PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { Field } from 'src/components/hook-form';

import ProductType from '../product-type';
import ProductMedia from '../product-media';

// Hook để lấy danh mục
function useCategories() {
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await fetchData('product_categories', {
          orderBy: 'name',
          ascending: true,
        });

        if (result.success) {
          setCategories(result.data || []);
        } else {
          setError('Không thể tải danh mục sản phẩm');
          console.error('Failed to fetch categories:', result.error);
        }
      } catch (err) {
        setError('Lỗi khi tải danh mục sản phẩm');
        console.error('Error fetching categories:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, isLoading, error };
}

export default function SimpleProductForm({ watch, setValue, isEditMode = false }) {
  const { categories, isLoading, error } = useCategories();

  return (
    <Stack spacing={3}>

      {/* Thông tin cơ bản */}
      <Card sx={{ boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
            Thông tin cơ bản
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
              (Quan trọng)
            </Typography>
          </Typography>

          <Stack spacing={2}>
            {/* Tên sản phẩm */}
            <Field.Text
              name="name"
              label="Tên sản phẩm"
              required
              placeholder="Nhập tên sản phẩm"
            />

            {/* Danh mục */}
            <Box>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              <Field.Select
                name="categoryId"
                label="Danh mục"
                required
                placeholder="Chọn danh mục"
                disabled={isLoading}
              >
                <MenuItem value="">-- Chọn danh mục --</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Field.Select>
            </Box>

            {/* URL sản phẩm */}
            <Field.Text
              name="url"
              label="URL sản phẩm"
              placeholder="https://example.com/product-url"
              helperText="Đường dẫn tới trang sản phẩm (tùy chọn)"
            />

            {/* Mô tả ngắn */}
            <Field.Text
              name="shortDescription"
              label="Mô tả ngắn"
              placeholder="Nhập mô tả ngắn về sản phẩm..."
              multiline
              rows={2}
              helperText="Mô tả ngắn gọn về sản phẩm (tùy chọn)"
            />

            {/* Mô tả chi tiết */}
            <Field.Text
              name="description"
              label="Mô tả chi tiết"
              multiline
              rows={3}
              placeholder="Nhập mô tả chi tiết về sản phẩm..."
            />
          </Stack>
        </CardContent>
      </Card>

      {/* Hình ảnh */}
      <Card sx={{  boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
            Hình ảnh sản phẩm
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
              (Quan trọng)
            </Typography>
          </Typography>
          <ProductMedia watch={watch} setValue={setValue} />
        </CardContent>
      </Card>

      {/* Giá và tồn kho */}
      <Card sx={{ boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
            Giá và tồn kho
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
              (Quan trọng)
            </Typography>
          </Typography>

          <Stack spacing={2}>
            {/* Giá bán */}
            <Field.CurrencyInput
              name="price"
              label="Giá bán"
              required
              placeholder="0"
              InputProps={{
                endAdornment: 'VNĐ',
              }}
              helperText="Giá bán cho khách hàng"
              inputProps={{
                min: 0,
                step: 1000,
              }}
            />

            {/* Tối ưu: Hiển thị thông báo cho simple product */}
            {isEditMode && (
              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Sản phẩm đơn giản:</strong> Chỉ cần cấu hình thông tin cơ bản, hình ảnh và giá bán.
                </Typography>
              </Alert>
            )}
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}
