'use client';

import { fetchData, callRPC } from './supabase-utils';

/**
 * Business Intelligence Service
 * Provides advanced analytics and reporting capabilities
 */

// =====================================================
// 1. SALES ANALYTICS
// =====================================================

/**
 * Lấy báo cáo doanh thu theo thời gian
 */
export async function getSalesAnalytics(timeRange = '30d', businessType = null) {
  try {
    const query = `
      SELECT
        DATE_TRUNC('day', created_at) as date,
        COUNT(*) as order_count,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as avg_order_value,
        COUNT(DISTINCT customer_id) as unique_customers
      FROM orders
      WHERE created_at >= NOW() - INTERVAL '${timeRange}'
        AND status IN ('completed', 'delivered')
      GROUP BY DATE_TRUNC('day', created_at)
      ORDER BY date DESC
    `;

    const result = await executeQuery(query);

    return {
      success: true,
      data: result.data || [],
      summary: calculateSalesSummary(result.data || [])
    };
  } catch (error) {
    console.error('Error fetching sales analytics:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Tính toán tóm tắt doanh số
 */
function calculateSalesSummary(salesData) {
  if (!salesData.length) return null;

  const totalRevenue = salesData.reduce((sum, day) => sum + parseFloat(day.total_revenue || 0), 0);
  const totalOrders = salesData.reduce((sum, day) => sum + parseInt(day.order_count || 0), 0);
  const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  return {
    totalRevenue,
    totalOrders,
    avgOrderValue,
    dailyAverage: salesData.length > 0 ? totalRevenue / salesData.length : 0
  };
}

// =====================================================
// 2. PRODUCT PERFORMANCE ANALYTICS
// =====================================================

/**
 * Lấy báo cáo hiệu suất sản phẩm
 */
export async function getProductPerformance(limit = 20, businessType = null) {
  try {
    const query = `
      SELECT
        p.id,
        p.name,
        p.product_type,
        p.selling_price,
        COUNT(oi.id) as total_sold,
        SUM(oi.quantity) as total_quantity,
        SUM(oi.price * oi.quantity) as total_revenue,
        AVG(oi.price) as avg_selling_price,
        p.inventory_quantity as current_stock
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE o.status IN ('completed', 'delivered') OR o.id IS NULL
      GROUP BY p.id, p.name, p.product_type, p.selling_price, p.inventory_quantity
      ORDER BY total_revenue DESC NULLS LAST
      LIMIT ${limit}
    `;

    const result = await executeQuery(query);

    return {
      success: true,
      data: result.data || [],
      insights: generateProductInsights(result.data || [])
    };
  } catch (error) {
    console.error('Error fetching product performance:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Tạo insights cho sản phẩm
 */
function generateProductInsights(productData) {
  if (!productData.length) return null;

  const topPerformer = productData[0];
  const lowStock = productData.filter(p => p.current_stock < 10);
  const noSales = productData.filter(p => p.total_sold === 0);

  return {
    topPerformer,
    lowStockCount: lowStock.length,
    noSalesCount: noSales.length,
    totalProducts: productData.length
  };
}

// =====================================================
// 3. CUSTOMER ANALYTICS
// =====================================================

/**
 * Lấy phân tích khách hàng
 */
export async function getCustomerAnalytics(timeRange = '30d') {
  try {
    const query = `
      SELECT
        c.id,
        c.email,
        c.name,
        COUNT(o.id) as total_orders,
        SUM(o.total_amount) as total_spent,
        AVG(o.total_amount) as avg_order_value,
        MAX(o.created_at) as last_order_date,
        MIN(o.created_at) as first_order_date
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
      WHERE o.created_at >= NOW() - INTERVAL '${timeRange}' OR o.id IS NULL
      GROUP BY c.id, c.email, c.name
      ORDER BY total_spent DESC NULLS LAST
    `;

    const result = await executeQuery(query);

    return {
      success: true,
      data: result.data || [],
      segments: segmentCustomers(result.data || [])
    };
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Phân đoạn khách hàng
 */
function segmentCustomers(customerData) {
  if (!customerData.length) return null;

  const vip = customerData.filter(c => c.total_spent > 5000000); // > 5M VND
  const regular = customerData.filter(c => c.total_spent >= 1000000 && c.total_spent <= 5000000); // 1M-5M VND
  const newCustomers = customerData.filter(c => c.total_orders <= 1);
  const inactive = customerData.filter(c => {
    if (!c.last_order_date) return true;
    const daysSinceLastOrder = (new Date() - new Date(c.last_order_date)) / (1000 * 60 * 60 * 24);
    return daysSinceLastOrder > 90;
  });

  return {
    vip: vip.length,
    regular: regular.length,
    newCustomers: newCustomers.length,
    inactive: inactive.length,
    total: customerData.length
  };
}

// =====================================================
// 4. INVENTORY ANALYTICS
// =====================================================

/**
 * Lấy phân tích tồn kho
 */
export async function getInventoryAnalytics() {
  try {
    const query = `
      SELECT
        p.id,
        p.name,
        p.sku,
        p.inventory_quantity as current_stock,
        p.price,
        p.inventory_quantity * p.price as stock_value,
        CASE
          WHEN p.inventory_quantity = 0 THEN 'out_of_stock'
          WHEN p.inventory_quantity <= 10 THEN 'low_stock'
          WHEN p.inventory_quantity <= 50 THEN 'medium_stock'
          ELSE 'high_stock'
        END as stock_status,
        -- Tính toán dự báo hết hàng dựa trên doanh số 30 ngày qua
        COALESCE(
          p.inventory_quantity / NULLIF(
            (SELECT AVG(daily_sold) FROM (
              SELECT DATE_TRUNC('day', o.created_at) as date, SUM(oi.quantity) as daily_sold
              FROM orders o
              JOIN order_items oi ON o.id = oi.order_id
              WHERE oi.product_id = p.id
                AND o.created_at >= NOW() - INTERVAL '30 days'
                AND o.status IN ('completed', 'delivered')
              GROUP BY DATE_TRUNC('day', o.created_at)
            ) daily_sales), 0
          ), 999
        ) as days_until_stockout
      FROM products p
      WHERE p.product_type IN ('simple', 'variable')
      ORDER BY stock_value DESC
    `;

    const result = await executeQuery(query);

    return {
      success: true,
      data: result.data || [],
      summary: calculateInventorySummary(result.data || [])
    };
  } catch (error) {
    console.error('Error fetching inventory analytics:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Tính toán tóm tắt tồn kho
 */
function calculateInventorySummary(inventoryData) {
  if (!inventoryData.length) return null;

  const totalValue = inventoryData.reduce((sum, item) => sum + parseFloat(item.stock_value || 0), 0);
  const outOfStock = inventoryData.filter(item => item.stock_status === 'out_of_stock').length;
  const lowStock = inventoryData.filter(item => item.stock_status === 'low_stock').length;
  const criticalItems = inventoryData.filter(item => item.days_until_stockout <= 7);

  return {
    totalValue,
    totalItems: inventoryData.length,
    outOfStock,
    lowStock,
    criticalItems: criticalItems.length
  };
}

// =====================================================
// 5. BUSINESS TYPE SPECIFIC ANALYTICS
// =====================================================

/**
 * Lấy analytics theo business type
 */
export async function getBusinessTypeAnalytics(businessType) {
  switch (businessType) {
    case 'retail':
      return getRetailAnalytics();
    case 'digital':
      return getDigitalAnalytics();
    case 'services':
      return getServicesAnalytics();
    case 'hybrid':
      return getHybridAnalytics();
    default:
      return getGeneralAnalytics();
  }
}

/**
 * Analytics cho Retail business
 */
async function getRetailAnalytics() {
  // Tập trung vào inventory, shipping, physical products
  const [sales, inventory, shipping] = await Promise.all([
    getSalesAnalytics('30d', 'retail'),
    getInventoryAnalytics(),
    getShippingAnalytics()
  ]);

  return {
    type: 'retail',
    sales,
    inventory,
    shipping,
    insights: generateRetailInsights(sales, inventory, shipping)
  };
}

/**
 * Analytics cho Digital business
 */
async function getDigitalAnalytics() {
  // Tập trung vào downloads, licenses, digital delivery
  const [sales, downloads, licenses] = await Promise.all([
    getSalesAnalytics('30d', 'digital'),
    getDownloadAnalytics(),
    getLicenseAnalytics()
  ]);

  return {
    type: 'digital',
    sales,
    downloads,
    licenses,
    insights: generateDigitalInsights(sales, downloads, licenses)
  };
}

/**
 * Analytics cho Services business
 */
async function getServicesAnalytics() {
  // Tập trung vào appointments, staff utilization, service metrics
  const [sales, appointments, staff] = await Promise.all([
    getSalesAnalytics('30d', 'services'),
    getAppointmentAnalytics(),
    getStaffAnalytics()
  ]);

  return {
    type: 'services',
    sales,
    appointments,
    staff,
    insights: generateServicesInsights(sales, appointments, staff)
  };
}

/**
 * Analytics cho Hybrid business
 */
async function getHybridAnalytics() {
  // Kết hợp tất cả các loại analytics
  const [sales, products, customers, inventory] = await Promise.all([
    getSalesAnalytics('30d', 'hybrid'),
    getProductPerformance(20, 'hybrid'),
    getCustomerAnalytics('30d'),
    getInventoryAnalytics()
  ]);

  return {
    type: 'hybrid',
    sales,
    products,
    customers,
    inventory,
    insights: generateHybridInsights(sales, products, customers, inventory)
  };
}

/**
 * General analytics cho business chưa setup type
 */
async function getGeneralAnalytics() {
  const [sales, products, customers] = await Promise.all([
    getSalesAnalytics('30d'),
    getProductPerformance(20),
    getCustomerAnalytics('30d')
  ]);

  return {
    type: 'general',
    sales,
    products,
    customers,
    insights: generateGeneralInsights(sales, products, customers)
  };
}

// =====================================================
// 7. HELPER ANALYTICS FUNCTIONS
// =====================================================

/**
 * Shipping Analytics (cho Retail)
 */
async function getShippingAnalytics() {
  try {
    const query = `
      SELECT
        shipping_method,
        COUNT(*) as shipment_count,
        AVG(shipping_cost) as avg_cost,
        AVG(EXTRACT(EPOCH FROM (delivered_at - shipped_at))/86400) as avg_delivery_days
      FROM orders
      WHERE shipped_at IS NOT NULL
        AND created_at >= NOW() - INTERVAL '30 days'
      GROUP BY shipping_method
      ORDER BY shipment_count DESC
    `;

    const result = await executeQuery(query);
    return { success: true, data: result.data || [] };
  } catch (error) {
    return { success: false, error: error.message, data: [] };
  }
}

/**
 * Download Analytics (cho Digital)
 */
async function getDownloadAnalytics() {
  try {
    const query = `
      SELECT
        p.name as product_name,
        COUNT(dl.id) as download_count,
        COUNT(DISTINCT dl.customer_id) as unique_downloaders,
        AVG(dl.file_size) as avg_file_size
      FROM digital_downloads dl
      JOIN products p ON dl.product_id = p.id
      WHERE dl.downloaded_at >= NOW() - INTERVAL '30 days'
      GROUP BY p.id, p.name
      ORDER BY download_count DESC
    `;

    const result = await executeQuery(query);
    return { success: true, data: result.data || [] };
  } catch (error) {
    return { success: false, error: error.message, data: [] };
  }
}

/**
 * License Analytics (cho Digital)
 */
async function getLicenseAnalytics() {
  try {
    const query = `
      SELECT
        license_type,
        COUNT(*) as license_count,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_licenses,
        COUNT(CASE WHEN expires_at < NOW() THEN 1 END) as expired_licenses
      FROM product_licenses
      WHERE created_at >= NOW() - INTERVAL '30 days'
      GROUP BY license_type
      ORDER BY license_count DESC
    `;

    const result = await executeQuery(query);
    return { success: true, data: result.data || [] };
  } catch (error) {
    return { success: false, error: error.message, data: [] };
  }
}

/**
 * Appointment Analytics (cho Services)
 */
async function getAppointmentAnalytics() {
  try {
    const query = `
      SELECT
        DATE_TRUNC('day', appointment_date) as date,
        COUNT(*) as total_appointments,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
        AVG(duration_minutes) as avg_duration
      FROM appointments
      WHERE appointment_date >= NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', appointment_date)
      ORDER BY date DESC
    `;

    const result = await executeQuery(query);
    return { success: true, data: result.data || [] };
  } catch (error) {
    return { success: false, error: error.message, data: [] };
  }
}

/**
 * Staff Analytics (cho Services)
 */
async function getStaffAnalytics() {
  try {
    const query = `
      SELECT
        s.name as staff_name,
        COUNT(a.id) as total_appointments,
        AVG(a.duration_minutes) as avg_appointment_duration,
        SUM(a.duration_minutes) as total_working_minutes
      FROM staff s
      LEFT JOIN appointments a ON s.id = a.staff_id
      WHERE a.appointment_date >= NOW() - INTERVAL '30 days'
      GROUP BY s.id, s.name
      ORDER BY total_appointments DESC
    `;

    const result = await executeQuery(query);
    return { success: true, data: result.data || [] };
  } catch (error) {
    return { success: false, error: error.message, data: [] };
  }
}

// =====================================================
// 8. INSIGHTS GENERATORS
// =====================================================

function generateRetailInsights(sales, inventory, shipping) {
  return {
    type: 'retail',
    recommendations: [
      inventory.summary?.lowStock > 5 ? 'Có nhiều sản phẩm sắp hết hàng, cần bổ sung tồn kho' : null,
      sales.summary?.avgOrderValue < 500000 ? 'Giá trị đơn hàng trung bình thấp, cân nhắc upselling' : null,
      shipping.data?.length > 0 ? 'Tối ưu phương thức vận chuyển để giảm chi phí' : null
    ].filter(Boolean),
    alerts: [
      inventory.summary?.outOfStock > 0 ? `${inventory.summary.outOfStock} sản phẩm đã hết hàng` : null,
      inventory.summary?.criticalItems > 0 ? `${inventory.summary.criticalItems} sản phẩm sắp hết trong 7 ngày` : null
    ].filter(Boolean)
  };
}

function generateDigitalInsights(sales, downloads, licenses) {
  return {
    type: 'digital',
    recommendations: [
      downloads.data?.length === 0 ? 'Chưa có download nào, kiểm tra hệ thống delivery' : null,
      licenses.data?.some(l => l.expired_licenses > 0) ? 'Có license đã hết hạn, cần renewal strategy' : null
    ].filter(Boolean),
    alerts: [
      sales.summary?.totalOrders === 0 ? 'Chưa có đơn hàng nào trong kỳ' : null
    ].filter(Boolean)
  };
}

function generateServicesInsights(sales, appointments, staff) {
  return {
    type: 'services',
    recommendations: [
      appointments.data?.some(a => a.cancelled > a.completed * 0.2) ? 'Tỷ lệ hủy lịch cao, cần cải thiện quy trình' : null,
      staff.data?.length === 0 ? 'Chưa có dữ liệu nhân viên, cần setup staff management' : null
    ].filter(Boolean),
    alerts: [
      appointments.data?.length === 0 ? 'Chưa có lịch hẹn nào được đặt' : null
    ].filter(Boolean)
  };
}

function generateHybridInsights(sales, products, customers, inventory) {
  return {
    type: 'hybrid',
    recommendations: [
      'Tối ưu mix sản phẩm physical/digital/service dựa trên performance',
      'Phân tích customer journey để cải thiện cross-selling',
      'Cân nhắc bundle products từ các category khác nhau'
    ],
    alerts: [
      inventory.summary?.outOfStock > 0 ? `${inventory.summary.outOfStock} sản phẩm physical đã hết hàng` : null,
      customers.segments?.inactive > customers.segments?.total * 0.3 ? 'Tỷ lệ khách hàng inactive cao' : null
    ].filter(Boolean)
  };
}

function generateGeneralInsights(sales, products, customers) {
  return {
    type: 'general',
    recommendations: [
      'Setup business type để có insights chính xác hơn',
      'Phân tích dữ liệu để xác định business model phù hợp',
      'Tối ưu product catalog và pricing strategy'
    ],
    alerts: [
      sales.summary?.totalRevenue === 0 ? 'Chưa có doanh thu, cần review strategy' : null
    ].filter(Boolean)
  };
}